//+------------------------------------------------------------------+
//|                                      TradingPipelineContainer.mqh |
//|                                            PipelineAdvance_v1     |
//|                                                                  |
//+------------------------------------------------------------------+
#property strict

#include "../mql4-lib-master/Collection/Vector.mqh"
#include "TradingPipeline.mqh"
#include "TradingEvent.mqh"

//+------------------------------------------------------------------+
//| 統一的交易流水線容器類                                           |
//| 合併了 CompositePipeline 和 PipelineGroup 的功能                |
//+------------------------------------------------------------------+
class TradingPipelineContainer : public ITradingPipeline
{
private:
    string m_name;                          // 容器名稱
    string m_type;                          // 容器類型
    string m_description;                   // 容器描述
    bool m_executed;                        // 執行狀態
    bool m_isEnabled;                       // 是否啟用
    Vector<ITradingPipeline*> m_pipelines;  // 流水線向量
    bool m_owned;                           // 是否擁有子流水線
    int m_maxPipelines;                     // 最大子流水線數量
    PipelineResult* m_last_result;          // 執行結果

public:
    // 構造函數
    TradingPipelineContainer(string name,
                           string description = "",
                           string type = "TradingPipelineContainer",
                           bool owned = false,
                           int maxPipelines = 50)
        : m_name(name),
          m_type(type),
          m_description(description),
          m_executed(false),
          m_isEnabled(true),
          m_pipelines(owned),
          m_owned(owned),
          m_maxPipelines(maxPipelines),
          m_last_result(new PipelineResult(false, "尚未執行", name, ERROR_LEVEL_INFO))
    {
    }

    // 析構函數
    virtual ~TradingPipelineContainer()
    {
        Clear();
        if(m_last_result != NULL)
        {
            delete m_last_result;
            m_last_result = NULL;
        }
    }

    // 添加子流水線
    bool AddPipeline(ITradingPipeline* pipeline)
    {
        if(pipeline == NULL)
        {
            m_last_result = new PipelineResult(false, "流水線為空", GetName(), ERROR_LEVEL_ERROR);
            return false;
        }

        if(m_pipelines.size() >= m_maxPipelines)
        {
            m_last_result = new PipelineResult(false, "已達到最大流水線數量限制", GetName(), ERROR_LEVEL_ERROR);
            return false;
        }

        if(!m_pipelines.add(pipeline))
        {
            m_last_result = new PipelineResult(false, "添加流水線失敗", GetName(), ERROR_LEVEL_ERROR);
            return false;
        }

        m_last_result = new PipelineResult(true, "成功添加流水線", GetName(), ERROR_LEVEL_INFO);
        return true;
    }

    // 移除子流水線
    bool RemovePipeline(ITradingPipeline* pipeline)
    {
        if(pipeline == NULL)
        {
            m_last_result = new PipelineResult(false, "流水線為空", GetName(), ERROR_LEVEL_ERROR);
            return false;
        }

        if(!m_pipelines.remove(pipeline))
        {
            m_last_result = new PipelineResult(false, "移除流水線失敗", GetName(), ERROR_LEVEL_WARNING);
            return false;
        }

        m_last_result = new PipelineResult(true, "成功移除流水線", GetName(), ERROR_LEVEL_INFO);
        return true;
    }



    // 按索引獲取子流水線
    ITradingPipeline* GetPipeline(int index, ITradingPipeline* defaultValue = NULL)
    {
        if(index >= 0 && index < m_pipelines.size())
        {
            m_last_result = new PipelineResult(true, "成功獲取流水線", GetName(), ERROR_LEVEL_INFO);
            return m_pipelines.get(index);
        }

        m_last_result = new PipelineResult(false, "索引超出範圍", GetName(), ERROR_LEVEL_WARNING);
        return defaultValue;
    }

    // 清空所有子流水線
    void Clear()
    {
        m_pipelines.clear();
        m_last_result = new PipelineResult(true, "已清空所有流水線", GetName(), ERROR_LEVEL_INFO);
    }

    // 獲取子流水線數量
    int GetPipelineCount() const
    {
        return m_pipelines.size();
    }

    // 獲取最大子流水線數量
    int GetMaxPipelines() const
    {
        return m_maxPipelines;
    }

    // 檢查是否有指定流水線
    bool HasPipeline(ITradingPipeline* pipeline) const
    {
        if(pipeline == NULL) return false;

        foreachv(ITradingPipeline*, p, (Vector<ITradingPipeline*>*)GetPointer(m_pipelines))
        {
            if(p == pipeline) return true;
        }
        return false;
    }



    // 實現 ITradingPipeline 介面方法

    // 執行流水線
    virtual void Execute() override
    {
        if(m_executed || !m_isEnabled)
        {
            m_last_result = new PipelineResult(false,
                m_executed ? "已執行，跳過重複執行" : "容器已禁用，跳過執行",
                GetName(),
                ERROR_LEVEL_WARNING);
            return;
        }

        bool allSuccess = true;
        int executedCount = 0;

        foreachv(ITradingPipeline*, pipeline, GetPointer(m_pipelines))
        {
            if(pipeline != NULL)
            {
                pipeline.Execute();
                executedCount++;

                // 如果子流水線執行失敗，記錄但繼續執行其他流水線
                if(!pipeline.IsExecuted())
                {
                    allSuccess = false;
                }
            }
        }

        m_executed = true;
        m_last_result = new PipelineResult(allSuccess,
            StringFormat("執行完成，共執行 %d 個流水線，%s",
                executedCount,
                allSuccess ? "全部成功" : "部分失敗"),
            GetName(),
            allSuccess ? ERROR_LEVEL_INFO : ERROR_LEVEL_WARNING);
    }

    // 獲取流水線名稱
    virtual string GetName() override
    {
        return m_name;
    }

    // 獲取流水線類型
    virtual string GetType() override
    {
        return m_type;
    }

    // 檢查是否已執行
    virtual bool IsExecuted() override
    {
        return m_executed;
    }

    // 重置所有子流水線
    virtual void Restore() override
    {
        m_executed = false;
        int restoredCount = 0;

        foreachv(ITradingPipeline*, pipeline, GetPointer(m_pipelines))
        {
            if(pipeline != NULL)
            {
                pipeline.Restore();
                restoredCount++;
            }
        }

        m_last_result = new PipelineResult(true,
            StringFormat("重置完成，共重置 %d 個流水線", restoredCount),
            GetName(),
            ERROR_LEVEL_INFO);
    }

    // 業務屬性管理方法

    // 獲取容器描述
    string GetDescription() const
    {
        return m_description;
    }



    // 啟用/禁用容器
    void SetEnabled(bool enabled)
    {
        m_isEnabled = enabled;
        m_last_result = new PipelineResult(true,
            StringFormat("容器已%s", enabled ? "啟用" : "禁用"),
            GetName(),
            ERROR_LEVEL_INFO);
    }

    bool IsEnabled() const
    {
        return m_isEnabled;
    }



    // 獲取執行結果
    PipelineResult* GetResult() const
    {
        return m_last_result;
    }

    // 獲取容器狀態信息
    string GetStatusInfo() const
    {
        string info = StringFormat(
            "容器名稱: %s\n"
            "類型: %s\n"
            "描述: %s\n"
            "狀態: %s\n"
            "已執行: %s\n"
            "流水線數量: %d/%d",
            m_name,
            m_type,
            m_description,
            m_isEnabled ? "啟用" : "禁用",
            m_executed ? "是" : "否",
            m_pipelines.size(),
            m_maxPipelines
        );
        return info;
    }

    // 檢查容器是否為空
    bool IsEmpty() const
    {
        return m_pipelines.size() == 0;
    }

    // 檢查容器是否已滿
    bool IsFull() const
    {
        return m_pipelines.size() >= m_maxPipelines;
    }

    // 獲取所有子流水線（用於遍歷）
    int GetAllPipelines(ITradingPipeline* &pipelines[])
    {
        int count = m_pipelines.size();
        ArrayResize(pipelines, count);

        for(int i = 0; i < count; i++)
        {
            pipelines[i] = m_pipelines.get(i);
        }

        return count;
    }

    // 檢查是否已執行（const 版本）
    bool IsExecuted() const
    {
        return m_executed;
    }
};

//+------------------------------------------------------------------+
//| 事件流水線容器類                                                 |
//| 繼承自 TradingPipelineContainer，增加事件類型支持               |
//+------------------------------------------------------------------+
class EventPipeline : public TradingPipelineContainer
{
private:
    ENUM_TRADING_EVENT m_event;            // 交易事件

public:
    // 構造函數
    EventPipeline(string name,
                 ENUM_TRADING_EVENT event,
                 string description = "",
                 string type = "EventPipeline",
                 bool owned = false,
                 int maxPipelines = 50)
        : TradingPipelineContainer(name, description, type, owned, maxPipelines),
          m_event(event)
    {
    }

    // 析構函數
    virtual ~EventPipeline() {}

    // 獲取交易事件
    ENUM_TRADING_EVENT GetEvent() const
    {
        return m_event;
    }
};

//+------------------------------------------------------------------+
//| 階段流水線容器類                                                 |
//| 繼承自 TradingPipelineContainer，增加階段類型支持               |
//+------------------------------------------------------------------+
class StagePipeline : public TradingPipelineContainer
{
private:
    ENUM_TRADING_STAGE m_stage;            // 交易階段

public:
    // 構造函數
    StagePipeline(string name,
                 ENUM_TRADING_STAGE stage,
                 string description = "",
                 string type = "StagePipeline",
                 bool owned = false,
                 int maxPipelines = 50)
        : TradingPipelineContainer(name, description, type, owned, maxPipelines),
          m_stage(stage)
    {
    }

    // 析構函數
    virtual ~StagePipeline() {}

    // 獲取交易階段
    ENUM_TRADING_STAGE GetStage() const
    {
        return m_stage;
    }
};

#property strict

#include "TradingEvent.mqh"
#include "TradingPipeline.mqh"  // 包含 PipelineResult 定義
#include "TradingPipelineDriver.mqh"

//+------------------------------------------------------------------+
//| 前向聲明                                                         |
//+------------------------------------------------------------------+
class TradingPipelineDriver;
class TradingPipelineContainerManager;

//+------------------------------------------------------------------+
//| 常量定義                                                         |
//+------------------------------------------------------------------+
#define TRADING_CONTROLLER_NAME "TradingController"
#define TRADING_CONTROLLER_TYPE "Controller"
#define TRADING_CONTROLLER_VERSION "1.0.0"

//+------------------------------------------------------------------+
//| TradingController.mqh                                            |
//| 交易控制器 - EA 生命週期統一管理                                 |
//| 負責協調和控制 EA 的初始化、交易執行和清理過程                   |
//+------------------------------------------------------------------+

//+------------------------------------------------------------------+
//| 交易控制器類                                                     |
//| 作為 EA 生命週期的統一入口點，通過 TradingPipelineDriver         |
//| 管理所有交易流水線的執行                                         |
//+------------------------------------------------------------------+
class TradingController
{
private:
    // 核心組件
    TradingPipelineDriver* m_driver;            // 流水線驅動器

    // 狀態管理
    bool m_isInitialized;                       // 初始化狀態
    string m_name;                              // 控制器名稱
    string m_type;                              // 控制器類型
    PipelineResult* m_last_result;              // 最後執行結果

public:
    //+------------------------------------------------------------------+
    //| 構造函數                                                         |
    //| @param driver TradingPipelineDriver 實例，不能為 NULL            |
    //| @param name 控制器名稱，默認為 "TradingController"               |
    //+------------------------------------------------------------------+
    TradingController(TradingPipelineDriver* driver, string name = TRADING_CONTROLLER_NAME)
        : m_driver(driver),
          m_isInitialized(false),
          m_name(name),
          m_type(TRADING_CONTROLLER_TYPE),
          m_last_result(new PipelineResult(false, "控制器尚未初始化", name))
    {
        Print("[TradingController] 開始初始化控制器: " + m_name);

        // 檢查傳入的驅動器參數
        if(m_driver == NULL)
        {
            string errorMsg = "TradingPipelineDriver 參數不能為 NULL";
            Print("[錯誤] " + errorMsg);
            m_last_result = new PipelineResult(false, errorMsg, m_name);
        }
        else
        {
            Print("[TradingController] 成功接收 TradingPipelineDriver 實例");
            m_last_result = new PipelineResult(true, "控制器構造完成", m_name);
        }
    }

    //+------------------------------------------------------------------+
    //| 析構函數                                                         |
    //+------------------------------------------------------------------+
    virtual ~TradingController()
    {
        Print("[TradingController] 開始清理控制器: " + m_name);

        // 清理結果對象
        if(m_last_result != NULL)
        {
            delete m_last_result;
            m_last_result = NULL;
        }

        // 注意：不刪除 m_driver，因為它是外部傳入的實例，由外部管理生命週期
        m_driver = NULL;

        Print("[TradingController] 控制器清理完成: " + m_name);
    }

    //+------------------------------------------------------------------+
    //| EA 初始化方法                                                    |
    //| 負責初始化流水線驅動器並執行初始化流水線                         |
    //| @return ENUM_INIT_RETCODE 初始化結果代碼                         |
    //+------------------------------------------------------------------+
    ENUM_INIT_RETCODE OnInit()
    {
        Print("[TradingController] 開始 EA 初始化...");

        // 檢查驅動器是否可用
        if(m_driver == NULL)
        {
            string errorMsg = "TradingPipelineDriver 不可用";
            Print("[錯誤] " + errorMsg);
            m_last_result = new PipelineResult(false, errorMsg, m_name);
            return INIT_FAILED;
        }

        // 檢查驅動器是否已初始化
        if(!m_driver.IsInitialized())
        {
            string errorMsg = "TradingPipelineDriver 未初始化";
            Print("[錯誤] " + errorMsg);
            m_last_result = new PipelineResult(false, errorMsg, m_name);
            return INIT_FAILED;
        }

        // 執行初始化流水線
        bool initResult = ExecuteInitPipeline();

        if(initResult)
        {
            m_isInitialized = true;
            m_last_result = new PipelineResult(true, "EA 初始化成功", m_name);
            Print("[TradingController] EA 初始化成功");
            return INIT_SUCCEEDED;
        }
        else
        {
            m_isInitialized = false;
            string errorMsg = "EA 初始化流水線執行失敗";
            m_last_result = new PipelineResult(false, errorMsg, m_name);
            Print("[錯誤] " + errorMsg);
            return INIT_FAILED;
        }
    }

    //+------------------------------------------------------------------+
    //| EA 交易執行方法                                                  |
    //| 在每個 tick 時執行交易流水線                                     |
    //+------------------------------------------------------------------+
    void OnTick()
    {
        // 檢查初始化狀態
        if(!m_isInitialized)
        {
            string errorMsg = "控制器未初始化，跳過 OnTick 執行";
            m_last_result = new PipelineResult(false, errorMsg, m_name);
            return;
        }

        // 檢查驅動器可用性
        if(m_driver == NULL)
        {
            string errorMsg = "TradingPipelineDriver 不可用";
            m_last_result = new PipelineResult(false, errorMsg, m_name);
            return;
        }

        // 執行交易流水線
        bool tickResult = ExecuteTickPipeline();

        if(tickResult)
        {
            m_last_result = new PipelineResult(true, "OnTick 執行成功", m_name);
        }
        else
        {
            string errorMsg = "OnTick 流水線執行失敗";
            m_last_result = new PipelineResult(false, errorMsg, m_name);
        }
    }

    //+------------------------------------------------------------------+
    //| EA 清理方法                                                      |
    //| 在 EA 卸載時執行清理流水線                                       |
    //| @param reason 卸載原因代碼                                       |
    //+------------------------------------------------------------------+
    void OnDeinit(int reason)
    {
        Print("[TradingController] 開始 EA 清理，原因代碼: " + (string)reason);

        // 執行清理流水線
        bool deinitResult = ExecuteDeinitPipeline(reason);

        if(deinitResult)
        {
            m_last_result = new PipelineResult(true, "EA 清理成功", m_name);
            Print("[TradingController] EA 清理成功");
        }
        else
        {
            string errorMsg = "EA 清理流水線執行失敗";
            m_last_result = new PipelineResult(false, errorMsg, m_name);
            Print("[警告] " + errorMsg);
        }

        // 重置初始化狀態
        m_isInitialized = false;
    }

    //+------------------------------------------------------------------+
    //| 獲取初始化狀態                                                   |
    //| @return bool true表示已初始化，false表示未初始化                 |
    //+------------------------------------------------------------------+
    bool IsInitialized() const
    {
        return m_isInitialized;
    }

    //+------------------------------------------------------------------+
    //| 獲取控制器名稱                                                   |
    //| @return string 控制器名稱                                        |
    //+------------------------------------------------------------------+
    string GetName() const
    {
        return m_name;
    }

    //+------------------------------------------------------------------+
    //| 獲取控制器類型                                                   |
    //| @return string 控制器類型                                        |
    //+------------------------------------------------------------------+
    string GetType() const
    {
        return m_type;
    }

    //+------------------------------------------------------------------+
    //| 獲取最後執行結果                                                 |
    //| @return PipelineResult* 最後執行結果，永遠不為 NULL               |
    //+------------------------------------------------------------------+
    PipelineResult* GetResult() const
    {
        return m_last_result;
    }

    //+------------------------------------------------------------------+
    //| 獲取流水線驅動器                                                 |
    //| @return TradingPipelineDriver* 流水線驅動器實例                  |
    //+------------------------------------------------------------------+
    TradingPipelineDriver* GetDriver() const
    {
        return m_driver;
    }

private:
    //+------------------------------------------------------------------+
    //| 執行初始化流水線                                                 |
    //| @return bool true表示執行成功，false表示執行失敗                 |
    //+------------------------------------------------------------------+
    bool ExecuteInitPipeline()
    {
        Print("[TradingController] 執行初始化流水線...");

        if(m_driver == NULL)
        {
            Print("[錯誤] TradingPipelineDriver 不可用，無法執行初始化流水線");
            return false;
        }

        TradingPipelineContainerManager* manager = m_driver.GetManager();
        if(manager == NULL)
        {
            Print("[錯誤] 無法獲取 TradingPipelineContainerManager");
            return false;
        }

        // 執行 TRADING_INIT 事件的流水線
        manager.Execute(TRADING_INIT);

        // 檢查執行結果
        bool result = manager.IsExecuted();

        if(result)
        {
            Print("[TradingController] 初始化流水線執行成功");
        }
        else
        {
            Print("[錯誤] 初始化流水線執行失敗");
        }

        return result;
    }

    //+------------------------------------------------------------------+
    //| 執行交易流水線                                                   |
    //| @return bool true表示執行成功，false表示執行失敗                 |
    //+------------------------------------------------------------------+
    bool ExecuteTickPipeline()
    {
        if(m_driver == NULL)
        {
            return false;
        }

        TradingPipelineContainerManager* manager = m_driver.GetManager();
        if(manager == NULL)
        {
            return false;
        }

        // 執行 TRADING_TICK 事件的流水線
        manager.Execute(TRADING_TICK);

        // 檢查執行結果
        return manager.IsExecuted();
    }

    //+------------------------------------------------------------------+
    //| 執行清理流水線                                                   |
    //| @param reason 清理原因代碼                                       |
    //| @return bool true表示執行成功，false表示執行失敗                 |
    //+------------------------------------------------------------------+
    bool ExecuteDeinitPipeline(int reason)
    {
        if(m_driver == NULL)
        {
            return false;
        }

        TradingPipelineContainerManager* manager = m_driver.GetManager();
        if(manager == NULL)
        {
            return false;
        }

        // 執行 TRADING_DEINIT 事件的流水線
        manager.Execute(TRADING_DEINIT);

        // 檢查執行結果
        return manager.IsExecuted();
    }
};

# PipelineAdvance_v1 測試套件

## 📁 重構後的目錄結構

```
test/
├── README.md                                   # 本文檔
├── RunAllTests.mqh                            # 🎯 主要測試入口和邏輯
├── TestFramework.mqh                          # 🔧 測試框架
├── unit/                                      # 單元測試
│   ├── TestCompositePipeline.mqh             # CompositePipeline 測試
│   ├── TestPipelineGroupManager.mqh          # PipelineGroupManager 測試
│   ├── TestTradingPipelineContainer.mqh      # TradingPipelineContainer 測試
│   ├── TestTradingPipelineContainerManager.mqh # 容器管理器測試
│   └── TestTradingPipelineContainerManager_Updated.mqh # 更新版容器管理器測試
└── integration/                               # 整合測試
    ├── MockTradingPipeline.mqh               # 模擬流水線
    ├── SimpleTestRunner.mqh                  # 基本整合測試運行器
    ├── SimpleTestRunner_Updated.mqh          # 更新版整合測試運行器
    ├── SimpleTestRunner_v2.mqh               # v2 整合測試運行器
    ├── SimpleTestRunner_v2_Updated.mqh       # 更新版 v2 整合測試運行器
    ├── SimpleContainerTestRunner.mqh         # 容器整合測試運行器
    └── TestTradingPipelineContainerIntegration.mqh # 容器整合測試
```

## 🚀 使用方式

### 主要測試入口

```mql4
#include "Projects/EA_Wizard/module/PipelineAdvance_v1/test/RunAllTests.mqh"

void OnStart()
{
    // 運行完整測試套件
    RunAllPipelineAdvanceV1Tests();
}
```

### 可用的測試函數

1. **RunAllPipelineAdvanceV1Tests()** - 完整測試套件
2. **RunAllPipelineAdvanceV1TestsSimple()** - 簡化版測試
3. **QuickPipelineAdvanceV1Check()** - 快速檢查
4. **RunPipelineAdvanceV1UnitTests()** - 僅單元測試
5. **RunPipelineAdvanceV1IntegrationTests()** - 僅整合測試
6. **RunSimpleTestRunnerV2Only()** - 僅 v2 整合測試
7. **CompareSimpleTestRunners()** - 比較測試版本
8. **RunPipelineGroupManagerFocusedTests()** - 專項測試

## ✅ 重構成果

### 移除的文件（共約25個）
- ❌ 12個重複的 .mq4 測試入口文件
- ❌ 9個文檔 .md 文件
- ❌ 4個臨時和調試文件

### 保留的核心文件（共15個）
- ✅ 2個主要文件：RunAllTests.mqh, TestFramework.mqh
- ✅ 5個單元測試邏輯文件
- ✅ 8個整合測試邏輯文件

## 🎯 重構優勢

1. **簡化結構** - 移除重複和多餘文件
2. **統一入口** - 通過 RunAllTests.mqh 統一管理
3. **保持功能** - 100% 保留所有測試功能
4. **模組化設計** - 維持清晰的測試邏輯分離
5. **易於維護** - 減少文件數量，提高可維護性

## 📝 注意事項

- 所有測試功能通過 `RunAllTests.mqh` 訪問
- 測試框架功能在 `TestFramework.mqh` 中定義
- 單元測試和整合測試邏輯分別在對應目錄中
- 不再有多個重複的測試入口點
